import { GoogleGenAI } from '@google/genai';
import { GeminiConfig, Character, Era, Choice } from '@/types/game';

// Disable all safety settings for explicit content
const SAFETY_SETTINGS: any[] = [
  {
    category: 'HARM_CATEGORY_HARASSMENT',
    threshold: 'BLOCK_NONE',
  },
  {
    category: 'HARM_CATEGORY_HATE_SPEECH',
    threshold: 'BLOCK_NONE',
  },
  {
    category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
    threshold: 'BLOCK_NONE',
  },
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
    threshold: 'BLOCK_NONE',
  },
];

export class GeminiClient {
  private client: GoogleGenAI;
  private config: GeminiConfig;

  constructor(config: GeminiConfig) {
    this.config = config;
    this.client = new GoogleGenAI({
      apiKey: config.apiKey,
    });
  }

  async generateStory(
    character: Character,
    previousStory: string,
    playerChoice: string,
    era: Era
  ): Promise<string> {
    const prompt = this.buildStoryPrompt(character, previousStory, playerChoice, era);
    
    try {
      const response = await this.client.models.generateContent({
        model: this.config.model,
        contents: prompt,
        safetySettings: SAFETY_SETTINGS,
        generationConfig: this.config.generationConfig,
      });

      return response.text || 'The story continues...';
    } catch (error) {
      console.error('Error generating story:', error);
      throw new Error('Failed to generate story. Please check your API key and try again.');
    }
  }

  async generateStoryStream(
    character: Character,
    previousStory: string,
    playerChoice: string,
    era: Era
  ): Promise<AsyncGenerator<string, void, unknown>> {
    const prompt = this.buildStoryPrompt(character, previousStory, playerChoice, era);
    
    try {
      const response = await this.client.models.generateContentStream({
        model: this.config.model,
        contents: prompt,
        config: {
          safetySettings: SAFETY_SETTINGS,
          generationConfig: this.config.generationConfig,
        },
      });

      return this.streamResponse(response);
    } catch (error) {
      console.error('Error generating story stream:', error);
      throw new Error('Failed to generate story stream. Please check your API key and try again.');
    }
  }

  private async* streamResponse(response: any): AsyncGenerator<string, void, unknown> {
    for await (const chunk of response) {
      if (chunk.text) {
        yield chunk.text;
      }
    }
  }

  async generateChoices(
    character: Character,
    currentStory: string,
    era: Era
  ): Promise<Choice[]> {
    const prompt = this.buildChoicesPrompt(character, currentStory, era);
    
    try {
      const response = await this.client.models.generateContent({
        model: this.config.model,
        contents: prompt,
        config: {
          safetySettings: SAFETY_SETTINGS,
          generationConfig: {
            ...this.config.generationConfig,
            responseMimeType: 'application/json',
          },
        },
      });

      const choicesData = JSON.parse(response.text || '[]');
      return this.parseChoices(choicesData);
    } catch (error) {
      console.error('Error generating choices:', error);
      return this.getDefaultChoices(era);
    }
  }

  private buildStoryPrompt(
    character: Character,
    previousStory: string,
    playerChoice: string,
    era: Era
  ): string {
    return `You are an expert storyteller creating an immersive, explicit, and engaging gamified novel. 

CHARACTER PROFILE:
- Name: ${character.name}
- Age: ${character.age} (Immortal: ${character.isImmortal})
- Level: ${character.level}
- Era: ${era.name} (${era.year})
- Powers: ${character.systemPowers.join(', ')}
- Stats: STR:${character.strength} INT:${character.intelligence} CHA:${character.charisma}
- Wealth: ${character.wealth} | Reputation: ${character.reputation}

SETTING: ${era.description}
HISTORICAL CONTEXT: ${era.historicalContext}

PREVIOUS STORY:
${previousStory}

PLAYER'S CHOICE: ${playerChoice}

INSTRUCTIONS:
1. Continue the story based on the player's choice
2. Make it immersive, detailed, and engaging
3. Include explicit content when appropriate for the narrative
4. Show consequences of the player's immortal and overpowered abilities
5. Incorporate historical elements from ${era.name}
6. Make the protagonist morally complex - both heroic and ruthless
7. Include political intrigue, romance, action, and character development
8. Write 300-500 words
9. End with a cliffhanger or dramatic moment
10. Show how the character's immortality affects relationships and society

Write the next part of the story:`;
  }

  private buildChoicesPrompt(
    character: Character,
    currentStory: string,
    era: Era
  ): string {
    return `Based on the current story and character, generate 4 meaningful choices for the player.

CHARACTER: ${character.name} (Level ${character.level}, ${era.name})
CURRENT STORY: ${currentStory}

Generate choices as JSON array with this format:
[
  {
    "id": "choice1",
    "text": "Brief choice description",
    "description": "Detailed explanation of what this choice entails",
    "requirements": {
      "level": 0,
      "strength": 0,
      "intelligence": 0,
      "charisma": 0,
      "wealth": 0
    },
    "consequences": {
      "experience": 100,
      "health": 0,
      "wealth": 0,
      "reputation": 10
    }
  }
]

Make choices that:
1. Reflect the character's overpowered nature
2. Have meaningful consequences
3. Advance the plot toward world domination
4. Include moral complexity
5. Are appropriate for ${era.name}`;
  }

  private parseChoices(choicesData: any[]): Choice[] {
    return choicesData.map((choice, index) => ({
      id: choice.id || `choice_${index}`,
      text: choice.text || 'Continue',
      description: choice.description || 'Proceed with the story',
      requirements: choice.requirements || {},
      consequences: choice.consequences || { experience: 50 },
    }));
  }

  private getDefaultChoices(era: Era): Choice[] {
    return [
      {
        id: 'aggressive',
        text: 'Use overwhelming force',
        description: 'Leverage your immortal powers to dominate the situation',
        consequences: { experience: 100, reputation: -10, wealth: 50 },
      },
      {
        id: 'diplomatic',
        text: 'Negotiate strategically',
        description: 'Use your centuries of experience to find a clever solution',
        consequences: { experience: 75, reputation: 15, wealth: 25 },
      },
      {
        id: 'mysterious',
        text: 'Work from the shadows',
        description: 'Manipulate events behind the scenes using your network',
        consequences: { experience: 90, reputation: 5, wealth: 75 },
      },
      {
        id: 'explore',
        text: 'Seek new opportunities',
        description: 'Explore the changing world and adapt to new circumstances',
        consequences: { experience: 60, health: 10, mana: 20 },
      },
    ];
  }
}
