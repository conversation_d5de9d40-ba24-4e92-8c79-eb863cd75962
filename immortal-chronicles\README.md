# Immortal Chronicles

An immersive, gamified interactive novel powered by Google's Gemini AI. Experience the journey of an immortal protagonist through the ages, from medieval India to the far future, wielding an overpowered system to conquer the world.

## 🎮 Game Features

- **Immortal Protagonist**: Play as an immortal being with an overpowered system
- **Historical Journey**: Progress through different eras from 1700s Mughal Empire to 2100s future India
- **AI-Powered Storytelling**: Dynamic story generation using Google Gemini AI
- **Character Progression**: Level up, gain new powers, and build relationships
- **Explicit Content**: Mature themes and content for engaging storytelling
- **Multiple AI Models**: Choose from various Gemini models for different storytelling styles
- **Real-time Streaming**: Watch stories unfold in real-time with streaming responses

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- A Google Gemini API key from [Google AI Studio](https://aistudio.google.com/apikey)

### Installation

1. Install dependencies:
```bash
npm install
```

2. Set up your environment:
```bash
cp .env.example .env.local
```

3. Add your Gemini API key to `.env.local` or configure it in the game settings

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🎯 How to Play

1. **Configure Settings**: Click the Settings button to enter your Gemini API key and select your preferred AI model
2. **Choose Your Path**: Read the story and select from available choices
3. **Watch Your Character Grow**: Gain experience, level up, and unlock new powers
4. **Progress Through Eras**: As you become more powerful, advance through different historical periods
5. **Build Your Empire**: Use your immortal nature and overpowered abilities to shape the world

## 🤖 AI Models Available

- **gemini-2.0-flash-exp**: Latest experimental model with enhanced capabilities
- **gemini-2.0-flash-thinking-exp**: Advanced reasoning model with thinking capabilities
- **gemini-1.5-pro**: Balanced model for complex storytelling
- **gemini-1.5-flash**: Fast and efficient for quick responses

## 🏛️ Historical Eras

1. **Mughal Empire (1700)**: The height of Mughal power under Aurangzeb
2. **British Colonial Period (1800)**: East India Company's growing influence
3. **Independence Movement (1920)**: The struggle for freedom intensifies
4. **Modern India (1980)**: Post-independence development and growth
5. **Digital Age (2020)**: Information technology revolution
6. **Future India (2100)**: A transformed world under your influence

## ⚙️ Game Mechanics

### Character Stats
- **Level**: Increases with experience gained from choices
- **Health & Mana**: Core resources for survival and power usage
- **Strength, Intelligence, Charisma**: Core attributes affecting available choices
- **Wealth & Reputation**: Social and economic influence in the world
- **System Powers**: Special abilities granted by the immortal system

### Choice System
- Choices have requirements based on your character stats
- Each choice has consequences affecting your character development
- Some choices are only available with specific powers or high stats
- Difficulty setting affects the severity of consequences

### Era Progression
- Advance to new eras by reaching level milestones (every 20 levels)
- Each era offers unique challenges and opportunities
- Historical context influences available actions and story elements

## 🛠️ Technical Features

- **Next.js 15**: Modern React framework with App Router
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Utility-first CSS framework for medieval/fantasy theming
- **Google Gemini AI**: Advanced AI for dynamic story generation
- **Real-time Streaming**: Live story generation with streaming responses
- **Local Storage**: Automatic game state saving and loading
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## ⚠️ Content Warning

This game contains mature themes including:
- Violence and political conflict
- Adult relationships and explicit content
- Morally complex scenarios and choices
- Historical events and their consequences
- Power fantasy elements with immortal protagonist

Player discretion is advised. The game disables AI safety filters for mature storytelling.
